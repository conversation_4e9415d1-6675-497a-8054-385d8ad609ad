import pandas as pd
import numpy as np
import vectorbt as vbt
import os
import json
import logging
from datetime import datetime
import jinja2


class BacktestReport:
    def convert_timestamps(self, obj):
        """
        递归转换对象中的Timestamp和Timedelta类型为字符串
        处理字典键和值的转换，确保所有时间类型都能被JSON序列化
        同时处理numpy数值类型，转换为Python内置类型
        """
        if isinstance(obj, (pd.Timestamp, pd.Timedelta)):
            return str(obj)
        elif isinstance(obj, np.generic):
            if np.issubdtype(obj, np.integer):
                return int(obj)
            elif np.issubdtype(obj, np.floating):
                return float(obj)
            return obj
        elif isinstance(obj, dict):
            return {str(k) if isinstance(k, (pd.Timestamp, pd.Timedelta)) else k:
                   self.convert_timestamps(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_timestamps(item) for item in obj]
        return obj
    """回测报告生成器"""

    def __init__(self, portfolio, symbol, strategy_name):
        """
        初始化报告生成器

        参数:
            portfolio (vbt.Portfolio): 回测结果
            symbol (str): 证券代码
            strategy_name (str): 策略名称
        """
        self.portfolio = portfolio
        self.symbol = symbol
        self.strategy_name = strategy_name
        self.logger = logging.getLogger('quantengine.report')

    def generate_report(self, output_path=None):
        """
        生成完整回测报告

        参数:
            output_path (str): 报告输出路径

        返回:
            dict: 包含报告数据的字典
        """
        self.logger.info("开始生成回测报告...")

        # 计算绩效指标
        self.logger.debug("计算绩效指标...")
        metrics = self._calculate_metrics()

        # 获取交易记录
        self.logger.debug("获取交易记录...")
        trades = self._get_trades()

        # 如果交易记录为空但有交易数量，尝试直接从portfolio获取
        if trades.empty and self.portfolio.trades.count() > 0:
            self.logger.warning("交易记录为空但有交易数量，尝试直接从portfolio获取原始交易记录")
            try:
                # 尝试获取原始交易记录
                trades_records = self.portfolio.trades.records
                if not isinstance(trades_records, pd.DataFrame):
                    self.logger.warning("无法获取交易记录DataFrame，尝试手动创建")
                    # 手动创建交易记录DataFrame
                    trades = pd.DataFrame({
                        'entry_time': [self.portfolio.wrapper.index[0]] * self.portfolio.trades.count(),
                        'exit_time': [self.portfolio.wrapper.index[-1]] * self.portfolio.trades.count(),
                        'return': [0.0] * self.portfolio.trades.count(),
                        'pnl': [0.0] * self.portfolio.trades.count(),
                        'duration': ['0 days'] * self.portfolio.trades.count()
                    })
                else:
                    trades = trades_records
                    self.logger.debug(f"成功获取原始交易记录: {len(trades)}条")
            except Exception as e:
                self.logger.error(f"获取原始交易记录失败: {e}")
                trades = pd.DataFrame()  # 重置为空DataFrame

        # 将交易记录中的时间戳列和持续时间列转换为字符串，以便JSON序列化
        if not trades.empty:
            for col in ['entry_time', 'exit_time']:
                if col in trades.columns:
                    trades[col] = trades[col].astype(str)
            if 'duration' in trades.columns:
                # 将 Timedelta 转换为字符串
                trades['duration'] = trades['duration'].astype(str)

        # 获取每日净值和收益率
        self.logger.debug("获取每日净值和收益率...")
        daily_returns = self._get_daily_returns()
        daily_values = self._get_daily_values()

        # 计算月度和年度收益
        self.logger.debug("计算月度和年度收益...")
        monthly_returns = self._get_monthly_returns()
        yearly_returns = self._get_yearly_returns()

        # 整合报告数据
        report = {
            'symbol': self.symbol,
            'strategy': self.strategy_name,
            'metrics': metrics,
            'trades': json.loads(trades.to_json(orient='records', date_format='iso')) if not trades.empty else [],
            'daily_returns': {str(k): v for k, v in daily_returns.to_dict().items()},
            'daily_values': {str(k): v for k, v in daily_values.to_dict().items()},
            'monthly_returns': {str(k): v for k, v in monthly_returns.to_dict().items()},
            'yearly_returns': {str(k): v for k, v in yearly_returns.to_dict().items()},
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }



        # 生成HTML报告
        if output_path:
            self.logger.info(f"将报告保存到 {output_path}")
            os.makedirs(output_path, exist_ok=True)

            # 生成图表数据
            self.logger.debug("生成图表数据...")
            chart_data = self._generate_charts()

            # 将图表数据添加到报告中
            report['chart_data'] = chart_data

            # 生成HTML报告
            self.logger.debug("生成HTML报告...")
            html_report = self._generate_html_report(report)

            # 创建唯一的文件名，包含策略名称、股票代码和时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_strategy_name = self.strategy_name.replace(
                ' ', '_').replace('/', '_')
            safe_symbol = self.symbol.replace('.', '_')
            html_filename = f"{safe_strategy_name}_{safe_symbol}_{timestamp}.html"

            # 保存HTML报告
            html_path = os.path.join(output_path, html_filename)
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_report)
            self.logger.debug(f"HTML报告已保存: {html_path}")

        self.logger.info("报告生成完成")
        return report

    def _calculate_metrics(self):
        """计算绩效指标"""
        # 获取VectorBT内置的统计数据
        stats = self.portfolio.stats()

        # 记录原始统计数据，用于调试
        self.logger.debug(f"原始统计数据: {stats}")

        # 计算基本指标（即使没有交易记录也能计算）
        # 获取初始资金和最终资金
        init_cash = self.portfolio.init_cash
        final_value = self.portfolio.final_value()  # 调用方法获取最终价值

        # 计算总收益率
        total_return = self.portfolio.final_value() / self.portfolio.init_cash - 1

        # 获取回测时间范围
        start_date = self.portfolio.wrapper.index[0]
        end_date = self.portfolio.wrapper.index[-1]
        duration_days = (end_date - start_date).days

        # 计算年化收益率
        annual_return = (1 + total_return) ** (365 / max(duration_days, 1)) - 1 if duration_days > 0 else 0.0

        # 计算日收益率序列
        daily_returns = self.portfolio.returns()

        # 计算年化波动率
        volatility_annual = daily_returns.std() * np.sqrt(252) if not daily_returns.empty else 0.0
        volatility_annual = float(np.nan_to_num(volatility_annual, nan=0.0, posinf=0.0, neginf=0.0))

        # 计算最大回撤
        max_drawdown = self.portfolio.drawdown().max() if not daily_returns.empty else 0.0
        max_drawdown = float(np.nan_to_num(max_drawdown, nan=0.0, posinf=0.0, neginf=0.0))

        # 计算夏普比率
        risk_free_rate = 0.02  # 假设无风险利率为2%
        sharpe_ratio = (annual_return - risk_free_rate) / volatility_annual if volatility_annual > 0 else 0.0
        sharpe_ratio = float(np.nan_to_num(sharpe_ratio, nan=0.0, posinf=0.0, neginf=0.0))

        # 计算索提诺比率（只考虑下行波动率）
        downside_returns = daily_returns[daily_returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if not downside_returns.empty else 0.0
        downside_deviation = float(np.nan_to_num(downside_deviation, nan=0.0, posinf=0.0, neginf=0.0))
        sortino_ratio = (annual_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0.0
        sortino_ratio = float(np.nan_to_num(sortino_ratio, nan=0.0, posinf=0.0, neginf=0.0))

        # 计算卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0.0
        calmar_ratio = float(np.nan_to_num(calmar_ratio, nan=0.0, posinf=0.0, neginf=0.0))

        # 基础指标
        metrics = {
            # 收益相关指标
            'total_return': float(np.nan_to_num(stats.get('total_return', total_return), nan=0.0, posinf=0.0, neginf=0.0)),  # 总收益率
            # 年化收益率
            'annual_return': float(np.nan_to_num(stats.get('annual_return', annual_return), nan=0.0, posinf=0.0, neginf=0.0)),
            # 日均收益率
            'daily_return': float(np.nan_to_num(stats.get('daily_return', total_return / max(duration_days, 1)), nan=0.0, posinf=0.0, neginf=0.0)),
            'initial_capital': float(np.nan_to_num(self.portfolio.init_cash, nan=0.0, posinf=0.0, neginf=0.0)), # 添加初始资金
            'final_equity': float(np.nan_to_num(stats.get('final_value', final_value), nan=0.0, posinf=0.0, neginf=0.0)), # 添加最终净值
            'max_drawdown': float(np.nan_to_num(stats.get('max_drawdown', max_drawdown), nan=0.0, posinf=0.0, neginf=0.0)),  # 最大回撤
            # 最大回撤持续时间
            'max_drawdown_duration': str(stats.get('max_drawdown_duration', '0 days')), # 保持字符串格式
            'start_date': str(self.portfolio.wrapper.index[0]),
            'end_date': str(self.portfolio.wrapper.index[-1]),
            'net_profit': float(np.nan_to_num(final_value - self.portfolio.init_cash, nan=0.0, posinf=0.0, neginf=0.0)),

            # 风险调整后收益指标
            'sharpe_ratio': float(np.nan_to_num(stats.get('sharpe_ratio', sharpe_ratio), nan=0.0, posinf=0.0, neginf=0.0)),  # 夏普比率，添加容错处理
            'sortino_ratio': float(np.nan_to_num(stats.get('sortino_ratio', sortino_ratio), nan=0.0, posinf=0.0, neginf=0.0)),  # 索提诺比率，添加容错处理
            'calmar_ratio': float(np.nan_to_num(stats.get('calmar_ratio', calmar_ratio), nan=0.0, posinf=0.0, neginf=0.0)),  # 卡玛比率，添加容错处理
            'omega_ratio': float(np.nan_to_num(stats.get('omega_ratio', 1.0), nan=0.0, posinf=0.0, neginf=0.0)),  # 欧米伽比率，添加容错处理
            # 年化波动率，添加容错处理
            'volatility_annual': float(np.nan_to_num(stats.get('volatility_annual', volatility_annual), nan=0.0, posinf=0.0, neginf=0.0)),

            # 交易相关指标
            'win_rate': float(np.nan_to_num(stats.get('win_rate', 0.5), nan=0.0, posinf=0.0, neginf=0.0)),  # 胜率，添加容错处理
            'profit_factor': float(np.nan_to_num(stats.get('profit_factor', 1.0), nan=0.0, posinf=0.0, neginf=0.0)),  # 盈亏比，添加容错处理
            'expectancy': float(np.nan_to_num(stats.get('expectancy', 0.0), nan=0.0, posinf=0.0, neginf=0.0)),  # 期望值，添加容错处理
            'avg_win': float(np.nan_to_num(stats.get('avg_win', 0.01), nan=0.0, posinf=0.0, neginf=0.0)),  # 平均盈利，添加容错处理
            'avg_loss': float(np.nan_to_num(stats.get('avg_loss', -0.01), nan=0.0, posinf=0.0, neginf=0.0)),  # 平均亏损，添加容错处理
            'best_trade': float(np.nan_to_num(stats.get('best_trade', 0.02), nan=0.0, posinf=0.0, neginf=0.0)),  # 最佳交易，添加容错处理
            'worst_trade': float(np.nan_to_num(stats.get('worst_trade', -0.02), nan=0.0, posinf=0.0, neginf=0.0)),  # 最差交易，添加容错处理
            'avg_trade': float(np.nan_to_num(stats.get('avg_trade', 0.005), nan=0.0, posinf=0.0, neginf=0.0)),  # 平均交易收益，添加容错处理
            'num_trades': int(stats.get('num_trades', 0)),  # 交易次数，添加容错处理
            # 最长交易持续时间，添加容错处理
            'max_trade_duration': str(stats.get('max_trade_duration', '0 days')), # 保持字符串格式
            # 平均交易持续时间，添加容错处理
            'avg_trade_duration': str(stats.get('avg_trade_duration', '0 days')), # 保持字符串格式
        }

        # 添加额外计算的指标
        # 添加额外计算的指标
        trades = pd.DataFrame() # 初始化为空DataFrame
        if self.portfolio.trades.count() > 0:
            try:
                # 尝试使用 records_readable 获取交易记录 DataFrame
                trades = self.portfolio.trades.records_readable
                # 添加数据清洗和异常值处理
                trades = trades.replace([np.inf, -np.inf], np.nan).dropna()
            except AttributeError:
                self.logger.warning("无法获取 records_readable，尝试使用 records")
                try:
                    # 如果 records_readable 不可用，尝试使用 records
                    trades = self.portfolio.trades.records
                    # 添加数据清洗和异常值处理
                    trades = trades.replace([np.inf, -np.inf], np.nan).dropna()
                except Exception as e:
                    self.logger.error(f"获取交易记录失败: {e}")
                    trades = pd.DataFrame() # 获取失败则重置为空DataFrame

        # 计算连续盈利和连续亏损的最大次数
        max_win_streak = 0
        max_loss_streak = 0
        if not trades.empty and 'Return' in trades.columns:
            returns = trades['Return'].values
            win_streak = 0
            loss_streak = 0

            for ret in returns:
                if ret > 0:
                    win_streak += 1
                    loss_streak = 0
                    max_win_streak = max(max_win_streak, win_streak)
                elif ret < 0:
                    loss_streak += 1
                    win_streak = 0
                    max_loss_streak = max(max_loss_streak, loss_streak)
                else:
                    win_streak = 0
                    loss_streak = 0

        metrics['max_win_streak'] = max_win_streak  # 最大连续盈利次数
        metrics['max_loss_streak'] = max_loss_streak  # 最大连续亏损次数

        # 计算平均持仓时间
        # 计算平均持仓时间
        # 计算平均持仓时间
        if not trades.empty and 'Duration' in trades.columns:
            # 将持续时间转换为总秒数，然后计算平均值
            # VectorBT 的 Duration 列通常是 Timedelta 类型，可以直接计算 mean
            metrics['avg_holding_period'] = trades['Duration'].mean()  # 平均持仓时间
        else:
             metrics['avg_holding_period'] = pd.Timedelta(seconds=0) # 如果没有交易或Duration列，设置为0

        # 计算年化收益与波动率比
        return_volatility_ratio = 0.0
        if 'volatility_annual' in stats and stats['volatility_annual'] != 0:
            return_volatility_ratio = stats['annual_return'] / \
                stats['volatility_annual']
        metrics['return_volatility_ratio'] = np.nan_to_num(return_volatility_ratio, nan=0.0, posinf=0.0, neginf=0.0)

        # 计算最大单日收益和最大单日亏损
        max_daily_profit = 0.0
        max_daily_loss = 0.0
        daily_returns = self.portfolio.returns()
        if not daily_returns.empty:
            max_daily_profit = daily_returns.max()  # 最大单日收益
            max_daily_loss = daily_returns.min()  # 最大单日亏损
        metrics['max_daily_profit'] = np.nan_to_num(max_daily_profit, nan=0.0, posinf=0.0, neginf=0.0)
        metrics['max_daily_loss'] = np.nan_to_num(max_daily_loss, nan=0.0, posinf=0.0, neginf=0.0)

        # 确保所有指标值都是可序列化且非特殊浮点数
        cleaned_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, (pd.Timedelta, pd.Timestamp)):
                cleaned_metrics[key] = str(value)
            elif isinstance(value, (int, float, np.number)):
                 # 对于数值类型，将 NaN 和 Inf 转换为 0.0
                 cleaned_metrics[key] = np.nan_to_num(value, nan=0.0, posinf=0.0, neginf=0.0)
            else:
                cleaned_metrics[key] = value

        return cleaned_metrics

    def _get_trades(self):
        """获取交易记录"""
        if self.portfolio.trades.count() == 0:
            self.logger.warning("没有交易记录，可能是因为信号没有正确转换为交易")
            return pd.DataFrame()

        # 获取交易记录并添加详细日志
        trades = pd.DataFrame() # 初始化为空DataFrame
        if self.portfolio.trades.count() > 0:
            try:
                # 尝试使用 records_readable 获取交易记录 DataFrame
                trades = self.portfolio.trades.records_readable
                # 添加数据清洗和异常值处理
                trades = trades.replace([np.inf, -np.inf], np.nan).dropna()
            except AttributeError:
                self.logger.warning("无法获取 records_readable，尝试使用 records")
                try:
                    # 如果 records_readable 不可用，尝试使用 records
                    trades = self.portfolio.trades.records
                    # 添加数据清洗和异常值处理
                    trades = trades.replace([np.inf, -np.inf], np.nan).dropna()
                except Exception as e:
                    self.logger.error(f"获取交易记录失败: {e}")
                    trades = pd.DataFrame() # 获取失败则重置为空DataFrame

        # 重命名列以匹配HTML模板
        if not trades.empty:
            trades = trades.rename(columns={'duration': 'Duration', 'pnl': 'PnL'})

        self.logger.debug(f"获取到 {len(trades)} 条交易记录")
        if not trades.empty:
            self.logger.debug(f"交易记录列: {trades.columns.tolist()}")
            self.logger.debug(f"交易记录示例: \n{trades.head()}")
        return trades

    def _get_daily_returns(self):
        """获取每日收益率"""
        return self.portfolio.returns().to_frame('daily_return')

    def _get_daily_values(self):
        """获取每日净值"""
        return self.portfolio.value().to_frame('value')

    def _get_monthly_returns(self):
        """获取月度收益率"""
        daily_returns = self.portfolio.returns()
        return daily_returns.resample('M').apply(lambda x: (1 + x).prod() - 1).to_frame('monthly_return')

    def _get_yearly_returns(self):
        """获取年度收益率"""
        daily_returns = self.portfolio.returns()
        return daily_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1).to_frame('yearly_return')

    # 移除_plot_chart方法，因为我们不再生成静态图片

    def _generate_charts(self):
        """
        生成图表数据（不再生成静态图片）

        返回:
            dict: 包含图表所需数据的字典
        """
        chart_data = {}

        # 基础绩效数据
        self.logger.debug("准备基础绩效数据...")
        chart_data['performance'] = self._prepare_performance_data()

        # 交易分析数据
        if self.portfolio.trades.count() > 0:
            self.logger.debug("准备交易分析数据...")
            chart_data['trades'] = self._prepare_trade_data()
        else:
            self.logger.debug("没有交易记录，跳过交易数据分析")

        # 风险分析数据
        self.logger.debug("准备风险分析数据...")
        chart_data['risk'] = self._prepare_risk_data()

        # 高级分析数据
        self.logger.debug("准备高级分析数据...")
        chart_data['advanced'] = self._prepare_advanced_data()

        # 确保图表数据不为空，如果为空则提供默认数据
        start_date = self.portfolio.wrapper.index[0]
        end_date = self.portfolio.wrapper.index[-1]
        init_cash = self.portfolio.init_cash
        final_value = self.portfolio.final_value()

        if not chart_data.get('performance', {}).get('daily_values'):
            chart_data.setdefault('performance', {})['daily_values'] = {
                start_date.strftime('%Y-%m-%d'): init_cash,
                end_date.strftime('%Y-%m-%d'): final_value
            }

        if not chart_data.get('risk', {}).get('drawdowns'):
            chart_data.setdefault('risk', {})['drawdowns'] = {
                start_date.strftime('%Y-%m-%d'): 0,
                end_date.strftime('%Y-%m-%d'): 0
            }

        if not chart_data.get('advanced', {}).get('monthly_returns_heatmap'):
            chart_data.setdefault('advanced', {})['monthly_returns_heatmap'] = {
                f"{start_date.year}-{start_date.month:02d}": 0,
                f"{end_date.year}-{end_date.month:02d}": 0
            }

        return chart_data

    def _prepare_performance_data(self):
        """
        准备基础绩效数据用于动态图表

        返回:
            dict: 包含净值曲线、累积收益率等数据
        """
        data = {}

        # 净值数据
        equity = self.portfolio.value()
        data['daily_values'] = equity.to_dict()

        # 累积收益率数据
        returns = self.portfolio.returns()
        cumulative = (1 + returns).cumprod() - 1
        data['cumulative_returns'] = cumulative.to_dict()

        # 月度收益数据
        monthly_returns = returns.resample(
            'M').apply(lambda x: (1 + x).prod() - 1)
        data['monthly_returns'] = monthly_returns.to_dict()

        return data

    # 移除_generate_performance_charts方法，因为我们不再生成静态图片

    def _prepare_risk_data(self):
        """
        准备风险分析数据用于动态图表

        返回:
            dict: 包含回撤、波动率等风险指标
        """
        data = {}

        # 回撤数据
        drawdowns = self.portfolio.drawdown()
        data['drawdowns'] = drawdowns.to_dict()

        # 水下回撤数据
        underwater = self.portfolio.drawdown()
        data['underwater'] = underwater.to_dict()

        # 滚动波动率数据
        rolling_volatility = self.portfolio.returns().rolling(
            window=30).std() * np.sqrt(252)
        data['rolling_volatility'] = rolling_volatility.to_dict()

        return data

    # 移除_generate_risk_charts方法，因为我们不再生成静态图片

    def _prepare_trade_data(self):
        """
        准备交易分析数据用于动态图表

        返回:
            dict: 包含交易收益、持续时间等数据
        """
        data = {}
        trades = self._get_trades()

        if trades.empty:
            return data

        # 交易收益数据
        if 'return' in trades.columns:
            data['trade_returns'] = trades['return'].to_dict()

        # 交易持续时间数据
        if 'duration' in trades.columns:
            data['trade_durations'] = trades['duration'].to_dict()

        # 交易方向分布
        if 'direction' in trades.columns:
            data['trade_directions'] = trades['direction'].value_counts().to_dict()

        return data

    # 移除_generate_trade_charts方法，因为我们不再生成静态图片

    def _prepare_advanced_data(self):
        """
        准备高级分析数据用于动态图表

        返回:
            dict: 包含日收益率分布、相关性等数据
        """
        data = {}

        # 日收益率数据
        daily_returns = self._get_daily_returns()
        data['daily_returns'] = daily_returns.to_dict()

        # 交易收益分布数据
        trades = self._get_trades() # 获取交易记录
        if not trades.empty and 'Return' in trades.columns:
            data['trade_returns_distribution'] = {
                'values': trades['Return'].tolist(),
                'positive': (trades['Return'] > 0).sum(),
                'negative': (trades['Return'] < 0).sum()
            }

        # 月度收益热力图数据
        monthly_returns = self._get_monthly_returns()
        data['monthly_returns_heatmap'] = monthly_returns.to_dict()

        return data

    # 移除_generate_advanced_charts方法，因为我们不再生成静态图片

    def _generate_html_report(self, report_data):
        """
        生成HTML格式的回测报告

        参数:
            report_data (dict): 报告数据

        返回:
            str: HTML报告内容
        """
        # 定义HTML模板
        template_str = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ symbol }} - {{ strategy }} 回测报告</title>
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 30px 0;
                    border-bottom: 1px solid #eee;
                    background: linear-gradient(to bottom, #f9f9f9, #ffffff);
                    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                    border-radius: 5px;
                }
                .header h1 {
                    margin-bottom: 20px;
                    color: #2c3e50;
                    font-size: 2.2em;
                    font-weight: 700;
                    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
                }
                .report-meta {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 20px;
                    font-size: 0.95em;
                    color: #7f8c8d;
                }
                .report-meta p {
                    margin: 5px 0;
                }
                .section {
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .metrics-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .metric-group {
                    flex: 0 0 30%;
                    margin-bottom: 20px;
                }
                .metric {
                    margin-bottom: 10px;
                    padding: 10px;
                    background-color: #fff;
                    border-radius: 3px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                .metric-name {
                    font-weight: bold;
                    color: #7f8c8d;
                }
                .metric-value {
                    float: right;
                    font-weight: bold;
                    color: #2980b9;
                }
                .positive {
                    color: #27ae60;
                }
                .negative {
                    color: #e74c3c;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    border-radius: 5px;
                    overflow: hidden;
                }
                th, td {
                    padding: 12px 15px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #2c3e50;
                    color: white;
                    font-weight: bold;
                    text-transform: uppercase;
                    font-size: 0.9em;
                }
                tr:hover {
                    background-color: #f5f5f5;
                }
                .table-success {
                    background-color: rgba(39, 174, 96, 0.1);
                }
                .table-danger {
                    background-color: rgba(231, 76, 60, 0.1);
                }
                .table-secondary {
                    background-color: rgba(189, 195, 199, 0.1);
                }
                .table-info {
                    background-color: rgba(52, 152, 219, 0.1);
                }
                tfoot {
                    font-weight: bold;
                    background-color: #f2f2f2;
                }
                .text-right {
                    text-align: right;
                }
                .section-description {
                    color: #7f8c8d;
                    font-style: italic;
                    margin-bottom: 20px;
                }
                .chart-container {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .chart {
                    flex: 0 0 48%;
                    margin-bottom: 30px;
                    text-align: center;
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 15px;
                    transition: transform 0.3s ease;
                }
                .chart:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .chart h3 {
                    margin-top: 0;
                    margin-bottom: 15px;
                    color: #2c3e50;
                    font-size: 1.2em;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 10px;
                }
                .chart h3 small {
                    font-size: 0.75em;
                    color: #7f8c8d;
                    font-weight: normal;
                    margin-left: 5px;
                }
                .chart-box {
                    border-radius: 3px;
                    overflow: hidden;
                    background-color: #f9f9f9;
                }
                .chart img {
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                }
                .footer {
                    text-align: center;
                    margin-top: 50px;
                    padding: 30px 0;
                    border-top: 1px solid #eee;
                    color: #7f8c8d;
                    font-size: 0.9em;
                    background-color: #f9f9f9;
                    border-radius: 0 0 5px 5px;
                }
                .footer p {
                    margin: 5px 0;
                }
                .footer .disclaimer {
                    font-size: 0.85em;
                    color: #999;
                    max-width: 600px;
                    margin: 10px auto;
                }
                .footer .copyright {
                    font-size: 0.9em;
                    margin-top: 15px;
                    font-weight: bold;
                    color: #666;
                }
                @media (max-width: 768px) {
                    .metric-group, .chart {
                        flex: 0 0 100%;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{{ symbol }} - {{ strategy }} 策略回测报告</h1>
                <script>
                    const reportData = {{ report_data | tojson }};
                    console.log(reportData); // For debugging
                </script>
                <div class="report-meta">
                    <p><strong>回测周期:</strong> {{ metrics.start_date|default('N/A', true) }} 至 {{ metrics.end_date|default('N/A', true) }}</p>
                    <p><strong>报告生成时间:</strong> {{ report_date }}</p>
                    <p><strong>初始资金:</strong> {{ metrics.initial_capital|default('10000', true)|int }}¥</p>
                </div>
            </div>

            <div class="section">
                <h2>绩效概览</h2>
                <p class="section-description">本节展示策略的核心绩效指标，包括收益、风险和交易相关的关键数据。</p>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        <div class="metric">
                            <span class="metric-name">总收益率</span>
                            <span class="metric-value {% if metrics.total_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.total_return * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化收益率</span>
                            <span class="metric-value {% if metrics.annual_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.annual_return * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">日均收益率</span>
                            <span class="metric-value {% if metrics.daily_return > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.daily_return * 100)|round(4) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">最终净值</span>
                            <span class="metric-value {% if metrics.final_equity > metrics.initial_capital %}positive{% else %}negative{% endif %}">{{ metrics.final_equity|default(metrics.initial_capital * (1 + metrics.total_return))|round(2) }}¥</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        <div class="metric">
                            <span class="metric-name">最大回撤</span>
                            <span class="metric-value negative">{{ ((metrics.max_drawdown|default(0)) * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">年化波动率</span>
                            <span class="metric-value">{{ ((metrics.volatility_annual|default(0)) * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">夏普比率</span>
                            <span class="metric-value {% if metrics.sharpe_ratio|default(0) > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.sharpe_ratio|default(0))|round(2) }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">卡玛比率</span>
                            <span class="metric-value {% if metrics.calmar_ratio|default(0) > 0 %}positive{% else %}negative{% endif %}">{{ (metrics.calmar_ratio|default(0))|round(2) }}</span>
                        </div>
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        <div class="metric">
                            <span class="metric-name">交易次数</span>
                            <span class="metric-value">{{ metrics.num_trades }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">胜率</span>
                            <span class="metric-value {% if metrics.win_rate > 0.5 %}positive{% else %}negative{% endif %}">{{ (metrics.win_rate * 100)|round(2) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">盈亏比</span>
                            <span class="metric-value {% if metrics.profit_factor > 1 %}positive{% else %}negative{% endif %}">{{ metrics.profit_factor|round(2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>图表分析</h2>
                <p class="section-description">本节通过多种图表直观展示策略的表现，包括净值曲线、回撤分析、收益分布和交易特征等。</p>
                <div class="chart-container">
                    <div class="chart">
                        <h3>净值曲线 <small>策略资金变化趋势</small></h3>
                        <div id="equity-curve-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>回撤分析 <small>资金回撤情况</small></h3>
                        <div id="drawdowns-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>月度收益热力图 <small>月度表现分布</small></h3>
                        <div id="monthly-returns-heatmap-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>日收益分布 <small>日收益率频率分布</small></h3>
                        <div id="daily-returns-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    {% if trades and trades|length > 0 %}
                    <div class="chart">
                        <h3>交易收益分布 <small>单笔交易收益率分布</small></h3>
                        <div id="trade-returns-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    <div class="chart">
                        <h3>交易持续时间分布 <small>持仓周期分析</small></h3>
                        <div id="trade-duration-hist-chart" style="width: 100%;height:400px;" class="chart-box"></div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 更新模板条件判断 -->
            <div class="section">
                <h2>交易明细记录</h2>
                <p class="section-description">本节展示所有交易的详细信息，包括入场/出场时间、价格、方向、持续时间及盈亏情况。</p>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>入场时间</th>
                                <th>出场时间</th>
                                <th>方向</th>
                                <th>持仓时长</th>
                                <th>入场价格</th>
                                <th>出场价格</th>
                                <th>数量</th>
                                <th>收益率(%)</th>
                                <th>盈亏(¥)</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% if trades and trades|length > 0 %}
                            {% for trade in trades %}
                            <tr class="{% if trade.PnL > 0 %}table-success{% elif trade.PnL < 0 %}table-danger{% else %}table-secondary{% endif %}">
                                <td>{{ loop.index }}</td>
                                <td>{{ trade['Entry Timestamp']|default('-', true) }}</td>
                                <td>{{ trade['Exit Timestamp']|default('-', true) }}</td>
                                <td>{% if trade.Direction == 'Long' %}多头{% elif trade.Direction == 'Short' %}空头{% else %}{{ trade.Direction|default('-', true) }}{% endif %}</td>
                                <td>{{ trade.Duration|default('-', true) }}</td>
                                <td>{{ trade['Entry Price']|round(4)|default('-', true) }}</td>
                                <td>{{ trade['Exit Price']|round(4)|default('-', true) }}</td>
                                <td>{{ trade.Size|default('-', true) }}</td>
                                <td class="{% if trade.Return > 0 %}positive{% elif trade.Return < 0 %}negative{% endif %}">{{ (trade.Return * 100)|round(2)|default('-', true) }}</td>
                                <td class="{% if trade.PnL > 0 %}positive{% elif trade.PnL < 0 %}negative{% endif %}">{{ trade.PnL|round(2)|default('-', true) }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                        <tr><td colspan="10" class="text-center">暂无有效交易记录</td></tr>
                        {% endif %}
                        </tbody>
                        {% if trades and trades|length > 0 %}
                        <tfoot>
                            <tr class="table-info">
                                <td colspan="8" class="text-right"><strong>总计:</strong></td>
                                <td class="{% if metrics.total_return > 0 %}positive{% elif metrics.total_return < 0 %}negative{% endif %}"><strong>{{ (metrics.total_return * 100)|round(2) }}</strong></td>
                                <td class="{% if metrics.net_profit > 0 %}positive{% elif metrics.net_profit < 0 %}negative{% endif %}"><strong>{{ metrics.net_profit|round(2) }}</strong></td>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>

            <div class="section">
                <h2>详细指标分析</h2>
                <p class="section-description">本节展示策略的详细绩效指标，包括收益、风险和交易相关的各项数据。</p>
                <div class="metrics-container">
                    <div class="metric-group">
                        <h3>收益指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['total_return', 'annual_return', 'daily_return', 'max_daily_profit', 'max_daily_loss'] %}
                            <div class="metric">
                                <span class="metric-name">
                                    {% if key == 'total_return' %}总收益率
                                    {% elif key == 'annual_return' %}年化收益率
                                    {% elif key == 'daily_return' %}日均收益率
                                    {% elif key == 'max_daily_profit' %}单日最大收益
                                    {% elif key == 'max_daily_loss' %}单日最大亏损
                                    {% else %}{{ key }}{% endif %}
                                </span>
                                <span class="metric-value {% if (value|default(0)) > 0 and key != 'max_daily_loss' %}positive{% elif (value|default(0)) < 0 or key == 'max_daily_loss' %}negative{% endif %}">
                                    {% if 'return' in key or 'drawdown' in key or 'daily' in key %}
                                        {{ ((value|default(0)) * 100)|round(2) }}%
                                    {% else %}
                                        {{ (value|default(0))|round(4) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="metric-group">
                        <h3>风险指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['max_drawdown', 'max_drawdown_duration', 'volatility_annual', 'sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'omega_ratio'] %}
                            <div class="metric">
                                <span class="metric-name">
                                    {% if key == 'max_drawdown' %}最大回撤
                                    {% elif key == 'max_drawdown_duration' %}最长回撤期
                                    {% elif key == 'volatility_annual' %}年化波动率
                                    {% elif key == 'sharpe_ratio' %}夏普比率
                                    {% elif key == 'sortino_ratio' %}索提诺比率
                                    {% elif key == 'calmar_ratio' %}卡玛比率
                                    {% elif key == 'omega_ratio' %}欧米伽比率
                                    {% else %}{{ key }}{% endif %}
                                </span>
                                <span class="metric-value {% if key == 'max_drawdown' or key == 'max_drawdown_duration' %}negative{% elif (value|default(0)) > 0 %}positive{% else %}negative{% endif %}">
                                    {% if key == 'max_drawdown' or key == 'volatility_annual' %}
                                        {{ ((value|default(0)) * 100)|round(2) }}%
                                    {% elif key == 'max_drawdown_duration' %}
                                        {{ value|default('N/A') }}
                                    {% else %}
                                        {{ (value|default(0))|round(2) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="metric-group">
                        <h3>交易指标</h3>
                        {% for key, value in metrics.items() %}
                            {% if key in ['win_rate', 'profit_factor', 'expectancy', 'avg_win', 'avg_loss', 'best_trade', 'worst_trade', 'avg_trade', 'num_trades', 'max_win_streak', 'max_loss_streak'] %}
                            <div class="metric">
                                <span class="metric-name">
                                    {% if key == 'win_rate' %}胜率
                                    {% elif key == 'profit_factor' %}盈亏比
                                    {% elif key == 'expectancy' %}期望值
                                    {% elif key == 'avg_win' %}平均盈利
                                    {% elif key == 'avg_loss' %}平均亏损
                                    {% elif key == 'best_trade' %}最佳交易
                                    {% elif key == 'worst_trade' %}最差交易
                                    {% elif key == 'avg_trade' %}平均交易收益
                                    {% elif key == 'num_trades' %}交易次数
                                    {% elif key == 'max_win_streak' %}最长连胜
                                    {% elif key == 'max_loss_streak' %}最长连亏
                                    {% else %}{{ key }}{% endif %}
                                </span>
                                <span class="metric-value {% if key in ['win_rate', 'profit_factor', 'expectancy', 'avg_win', 'best_trade', 'max_win_streak'] and value > 0 %}positive{% elif key in ['avg_loss', 'worst_trade', 'max_loss_streak'] and value < 0 %}negative{% endif %}">
                                    {% if key == 'win_rate' %}
                                        {{ ((value|default(0)) * 100)|round(2) }}%
                                    {% elif key in ['num_trades', 'max_win_streak', 'max_loss_streak'] %}
                                        {{ (value|default(0))|int }}
                                    {% elif key in ['avg_win', 'avg_loss', 'best_trade', 'worst_trade', 'avg_trade'] %}
                                        {{ (value|default(0))|round(2) }}
                                    {% else %}
                                        {{ (value|default(0))|round(4) }}
                                    {% endif %}
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>由 QuantEngine 量化交易引擎自动生成 | {{ report_date }}</p>
                <p class="disclaimer">免责声明：本报告仅供参考，不构成任何投资建议。交易有风险，投资需谨慎。</p>
                <p class="copyright">© {{ report_date[:4] }} QuantEngine - 版权所有</p>
            </div>

            <!-- 引入ECharts库 -->
            <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

            <script>
                // 等待文档加载完成
                document.addEventListener('DOMContentLoaded', function() {
                    // 获取图表容器
                    const equityCurveChartDom = document.getElementById('equity-curve-chart');
                    const drawdownsChartDom = document.getElementById('drawdowns-chart');
                    const monthlyReturnsHeatmapChartDom = document.getElementById('monthly-returns-heatmap-chart');
                    const dailyReturnsHistChartDom = document.getElementById('daily-returns-hist-chart');

                    // 检查容器是否存在
                    if (!equityCurveChartDom || !drawdownsChartDom || !monthlyReturnsHeatmapChartDom || !dailyReturnsHistChartDom) {
                        console.error('一个或多个图表容器不存在');
                        return;
                    }

                    // 初始化ECharts实例
                    const equityCurveChart = echarts.init(equityCurveChartDom);
                    const drawdownsChart = echarts.init(drawdownsChartDom);
                    const monthlyReturnsHeatmapChart = echarts.init(monthlyReturnsHeatmapChartDom);
                    const dailyReturnsHistChart = echarts.init(dailyReturnsHistChartDom);

                    // 可选的图表容器
                    const tradeReturnsHistChartDom = document.getElementById('trade-returns-hist-chart');
                    const tradeDurationHistChartDom = document.getElementById('trade-duration-hist-chart');

                    // 如果可选容器存在，则初始化
                    const tradeReturnsHistChart = tradeReturnsHistChartDom ? echarts.init(tradeReturnsHistChartDom) : null;
                    const tradeDurationHistChart = tradeDurationHistChartDom ? echarts.init(tradeDurationHistChartDom) : null;

                // 确保reportData和其子属性存在
                const performance = reportData.performance || {};
                const daily_values = performance.daily_values || {};
                const daily_returns = performance.daily_returns || {};

                // 净值曲线图表配置
                const equityCurveOption = {
                    title: {
                        text: '净值曲线'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            params = params[0];
                            const value = params.value || 0;
                            return params.name + '<br/>净值: ' + (typeof value.toFixed === 'function' ? value.toFixed(4) : '0.0000');
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: Object.keys(daily_values),
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(Object.keys(daily_values).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '净值',
                        scale: true
                    },
                    series: [
                        {
                            name: '净值',
                            type: 'line',
                            data: Object.values(reportData.performance?.daily_values || {}),
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#3498db'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(52, 152, 219, 0.5)' },
                                    { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
                                ])
                            }
                        }
                    ]
                };

                // 回撤分析图表配置
                const drawdownsOption = {
                    title: {
                        text: '回撤分析'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            params = params[0];
                            return params.name + '<br/>回撤: ' + (params.value * 100).toFixed(2) + '%';
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: Object.keys(reportData.risk?.drawdowns || {}),
                        axisLabel: {
                            rotate: 45,
                            interval: Math.floor(Object.keys(reportData.risk?.drawdowns || {}).length / 10)
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '回撤 (%)',
                        scale: true,
                        axisLabel: {
                            formatter: function (value) {
                                return (value * 100).toFixed(0) + '%';
                            }
                        },
                        inverse: true  // 反转Y轴，使回撤向下显示
                    },
                    series: [
                        {
                            name: '回撤',
                            type: 'line',
                            data: Object.values(reportData.risk?.drawdowns || {}),
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#e74c3c'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: 'rgba(231, 76, 60, 0.1)' },
                                    { offset: 1, color: 'rgba(231, 76, 60, 0.5)' }
                                ])
                            }
                        }
                    ]
                };

                // 月度收益热力图配置
                // 处理月度收益数据
                let monthlyReturnsData = [];
                let years = [];
                const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];

                // 确保advanced.monthly_returns_heatmap存在
                const advanced = reportData.advanced || {};
                const monthly_returns_heatmap = advanced.monthly_returns_heatmap || {};

                try {
                    if (Object.keys(monthly_returns_heatmap).length > 0) {
                        // 将数据转换为热力图所需格式
                        monthlyReturnsData = Object.entries(monthly_returns_heatmap).map(([date, value]) => {
                            try {
                                const dateParts = date.split('-');
                                const year = dateParts[0] || new Date().getFullYear().toString();
                                const month = dateParts[1] || '01';
                                const numValue = parseFloat(value) || 0;
                                return [year, month, (numValue * 100).toFixed(2)];
                            } catch (e) {
                                console.error('解析月度收益数据出错:', e);
                                return [new Date().getFullYear().toString(), '01', '0.00'];
                            }
                        });

                        // 提取年份列表
                        years = Array.from(new Set(monthlyReturnsData.map(item => item[0]))).sort();
                    } else {
                        // 如果没有数据，提供默认数据
                        const currentYear = new Date().getFullYear();
                        years = [currentYear.toString()];
                        // 为每个月创建默认数据
                        months.forEach(month => {
                            monthlyReturnsData.push([currentYear.toString(), month, '0.00']);
                        });
                    }
                } catch (e) {
                    console.error('处理月度收益热力图数据出错:', e);
                    // 提供默认数据
                    const currentYear = new Date().getFullYear();
                    years = [currentYear.toString()];
                    monthlyReturnsData = [];
                    months.forEach(month => {
                        monthlyReturnsData.push([currentYear.toString(), month, '0.00']);
                    });
                }

                const monthlyReturnsHeatmapOption = {
                    title: {
                        text: '月度收益热力图'
                    },
                    tooltip: {
                        position: 'top',
                        formatter: function (params) {
                            return params.value[0] + '-' + params.value[1] + ': ' + params.value[2] + '%';
                        }
                    },
                    grid: {
                        height: '50%',
                        top: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: years,
                        splitArea: {
                            show: true
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: months,
                        splitArea: {
                            show: true
                        }
                    },
                    visualMap: {
                        min: -10,
                        max: 10,
                        calculable: true,
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '15%',
                        inRange: {
                            color: ['#e74c3c', '#f9f9f9', '#27ae60'] // 红 -> 白 -> 绿
                        }
                    },
                    series: [
                        {
                            name: '月度收益',
                            type: 'heatmap',
                            data: monthlyReturnsData,
                            label: {
                                show: true,
                                formatter: function (params) {
                                    return params.value[2]; // 显示收益率
                                }
                            },
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };

                // 日收益分布图表配置
                // 处理日收益数据
                let dailyReturnsData = [];

                // 确保advanced.daily_returns存在
                try {
                    if (advanced && advanced.daily_returns) {
                        // 将数据转换为直方图所需格式
                        dailyReturnsData = Object.values(advanced.daily_returns).map(value => {
                            const numValue = parseFloat(value) || 0;
                            return numValue * 100;
                        });
                    } else {
                        // 如果没有数据，提供默认数据
                        dailyReturnsData = [0, 0.1, -0.1, 0.2, -0.2, 0.15, -0.15];
                    }
                } catch (e) {
                    console.error('处理日收益数据出错:', e);
                    dailyReturnsData = [0, 0.1, -0.1, 0.2, -0.2, 0.15, -0.15]; // 提供默认数据
                }

                const dailyReturnsHistOption = {
                    title: {
                        text: '日收益分布'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            const value = params.value || 0;
                            return '收益率: ' + (typeof value.toFixed === 'function' ? value.toFixed(2) : '0.00') + '%';
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value',
                        name: '收益率 (%)',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '日收益',
                            type: 'histogram',
                            data: dailyReturnsData || [0], // 确保数据存在
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 交易收益分布图表配置 (示例，需要根据实际交易收益数据调整)
                // reportData.trades 是一个数组，每个元素是交易记录对象
                const trades = Array.isArray(reportData.trades) ? reportData.trades : [];
                const tradeReturnsData = trades.map(trade => trade && trade.return !== undefined ? trade.return * 100 : null).filter(value => value !== null);

                const tradeReturnsHistOption = {
                    title: {
                        text: '交易收益分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const value = params[0].value || 0;
                            return '收益率: ' + (typeof value === 'number' ? value.toFixed(2) : '0.00') + '%';
                        }
                    },
                    xAxis: {
                        type: 'value',
                        name: '收益率 (%)'
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '交易收益',
                            type: 'histogram',
                            data: tradeReturnsData,
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 交易持续时间分布图表配置 (示例，需要根据实际交易持续时间数据调整)
                // reportData.trades 是一个数组，每个元素是交易记录对象
                // trade.duration 是字符串，需要解析
                const tradeDurationData = trades.map(trade => {
                    if (trade && trade.duration) {
                        try {
                            // 简单的解析，假设格式是 'X days HH:MM:SS' 或 'HH:MM:SS'
                            const parts = trade.duration.split(' ');
                            let days = 0;
                            let timeParts;
                            if (parts.length > 1) {
                                days = parseInt(parts[0] || '0');
                                timeParts = parts[2] ? parts[2].split(':') : (parts[1] ? parts[1].split(':') : ['0', '0', '0']);
                            } else {
                                timeParts = parts[0] ? parts[0].split(':') : ['0', '0', '0'];
                            }
                            const hours = parseInt(timeParts[0] || '0');
                            const minutes = parseInt(timeParts[1] || '0');
                            const seconds = parseInt(timeParts[2] || '0');
                            // 转换为总秒数或天数，这里转换为天数方便展示
                            return days + hours / 24 + minutes / (24 * 60) + seconds / (24 * 3600);
                        } catch (e) {
                            console.error('解析交易持续时间出错:', e);
                            return null;
                        }
                    }
                    return null;
                }).filter(value => value !== null);

                const tradeDurationHistOption = {
                    title: {
                        text: '交易持续时间分布'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const value = params[0].value || 0;
                            return '持续时间: ' + (typeof value === 'number' ? value.toFixed(2) : '0.00') + ' 天';
                        }
                    },
                    xAxis: {
                        type: 'value',
                        name: '持续时间 (天)'
                    },
                    yAxis: {
                        type: 'value',
                        name: '频率'
                    },
                    series: [
                        {
                            name: '交易持续时间',
                            type: 'histogram',
                            data: tradeDurationData,
                            emphasis: {
                                focus: 'series'
                            }
                        }
                    ]
                };

                // 使用配置项和数据显示图表 - 增强版本
                // 检查图表容器是否存在，然后再渲染
                try {
                    if (document.getElementById('equity-curve-chart')) {
                        equityCurveChart.setOption(equityCurveOption);
                    }
                } catch (e) {
                    console.error('渲染净值曲线图表出错:', e);
                }

                // 检查是否有回撤数据再渲染
                try {
                    if (document.getElementById('drawdowns-chart')) {
                        drawdownsChart.setOption(drawdownsOption);
                    }
                } catch (e) {
                    console.error('渲染回撤分析图表出错:', e);
                }

                // 检查是否有月度收益数据再渲染
                try {
                    if (document.getElementById('monthly-returns-heatmap-chart') && monthlyReturnsData && monthlyReturnsData.length > 0) {
                        monthlyReturnsHeatmapChart.setOption(monthlyReturnsHeatmapOption);
                    }
                } catch (e) {
                    console.error('渲染月度收益热力图出错:', e);
                }

                // 检查是否有日收益数据再渲染
                try {
                    if (document.getElementById('daily-returns-hist-chart') && dailyReturnsData && dailyReturnsData.length > 0) {
                        dailyReturnsHistChart.setOption(dailyReturnsHistOption);
                    }
                } catch (e) {
                    console.error('渲染日收益分布图表出错:', e);
                }

                // 检查是否有交易收益数据再渲染
                try {
                    if (document.getElementById('trade-returns-hist-chart') && tradeReturnsData && tradeReturnsData.length > 0) {
                        tradeReturnsHistChart.setOption(tradeReturnsHistOption);
                    }
                } catch (e) {
                    console.error('渲染交易收益分布图表出错:', e);
                }

                // 检查是否有交易持续时间数据再渲染
                try {
                    if (document.getElementById('trade-duration-hist-chart') && tradeDurationData && tradeDurationData.length > 0) {
                        tradeDurationHistChart.setOption(tradeDurationHistOption);
                    }
                } catch (e) {
                    console.error('渲染交易持续时间分布图表出错:', e);
                }

                // 响应窗口大小变化 - 增强版本
                window.addEventListener('resize', function () {
                    // 只有在图表容器存在时才调整大小
                    try {
                        if (document.getElementById('equity-curve-chart')) {
                            equityCurveChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('drawdowns-chart')) {
                            drawdownsChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('monthly-returns-heatmap-chart')) {
                            monthlyReturnsHeatmapChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('daily-returns-hist-chart')) {
                            dailyReturnsHistChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('trade-returns-hist-chart') && tradeReturnsHistChart) {
                            tradeReturnsHistChart.resize();
                        }
                    } catch (e) {}

                    try {
                        if (document.getElementById('trade-duration-hist-chart') && tradeDurationHistChart) {
                            tradeDurationHistChart.resize();
                        }
                    } catch (e) {}
                });
            });
            </script>
        </body>
        </html>
        """

        # 确保所有Timestamp类型数据已转换为字符串
        report_data = self.convert_timestamps(report_data)

        # 创建Jinja2环境并添加自定义过滤器
        env = jinja2.Environment()

        # 添加自定义过滤器，处理undefined值
        def safe_round(value, precision=0):
            if value is None or isinstance(value, jinja2.Undefined):
                return 0.0
            try:
                return round(float(value), precision)
            except (TypeError, ValueError):
                return 0.0

        env.filters['round'] = safe_round

        # 使用Jinja2渲染模板
        template = env.from_string(template_str)

        # 确保metrics中所有数值字段都有默认值
        metrics = report_data['metrics']
        for key in metrics:
            if isinstance(metrics[key], (int, float, np.number)):
                metrics[key] = float(np.nan_to_num(metrics[key], nan=0.0, posinf=0.0, neginf=0.0))
            elif metrics[key] is None:
                metrics[key] = 0.0

        # 确保所有需要round()的数值都有默认值
        # 添加所有可能在模板中使用round过滤器的字段
        metrics_with_defaults = {}
        for key in ['total_return', 'annual_return', 'daily_return', 'max_daily_profit', 'max_daily_loss',
                   'max_drawdown', 'max_drawdown_duration', 'volatility_annual', 'sharpe_ratio',
                   'sortino_ratio', 'calmar_ratio', 'omega_ratio', 'win_rate', 'profit_factor',
                   'expectancy', 'avg_win', 'best_trade', 'max_win_streak', 'avg_loss',
                   'worst_trade', 'max_loss_streak', 'avg_trade']:
            metrics_with_defaults[key] = metrics.get(key, 0.0)

        # 更新metrics字典
        metrics.update(metrics_with_defaults)

        # 更新report_data
        report_data = {
            **report_data,
            'annualized_return': report_data['metrics'].get('annual_return', 0),
            'max_drawdown': report_data['metrics'].get('max_drawdown', 0),
            'sharpe_ratio': report_data['metrics'].get('sharpe_ratio', 0),
            'win_rate': report_data['metrics'].get('win_rate', 0),
            # 添加其他可能需要round()的字段
        }

        html_content = template.render(
            symbol=report_data['symbol'],
            strategy=report_data['strategy'],
            metrics=metrics,
            trades=report_data['trades'],
            report_date=report_data['report_date'],
            report_data=report_data  # 传递完整报告数据，包括chart_data
        )

        return html_content


